# Machine Learning Model



## Getting started

To make it easy for you to get started with Git<PERSON>ab, here's a list of recommended next steps.

Already a pro? Just edit this # Legal Document Title Extraction System

## Overview

The R_ml-model project is a sophisticated Named Entity Recognition (NER) system specifically designed for extracting titles from legal documents. This Flask-based API leverages an ensemble of four custom-trained spaCy models combined with OpenAI embeddings to accurately identify and extract document titles from PDF pages. The system is optimized for legal document processing and provides high-accuracy title extraction capabilities for document management and organization.

## Key Features

### 🎯 **Ensemble NER Models**
- **Multi-Model Architecture**: Four specialized spaCy models working in ensemble
- **High Accuracy**: F1-score of 94.3% for title extraction
- **Robust Performance**: Multiple model versions for different document types
- **Custom Training**: Models specifically trained on legal document datasets

### 📄 **Document Processing**
- **PDF Page Processing**: Handles multi-page PDF documents
- **Text Extraction**: Processes extracted text from PDF pages
- **Title Identification**: Identifies document titles with high precision
- **Batch Processing**: Efficient processing of multiple pages

### 🔍 **Intelligent Title Matching**
- **Semantic Similarity**: OpenAI embedding-based title matching
- **Cosine Similarity**: Advanced similarity scoring for title selection
- **Context Awareness**: Considers document context for accurate title extraction
- **Fallback Mechanisms**: Multiple approaches for title identification

### 🚀 **API Integration**
- **RESTful API**: Simple HTTP endpoint for title extraction
- **JSON Interface**: Structured input/output format
- **Error Handling**: Comprehensive error management and logging
- **Scalable Architecture**: Designed for high-throughput processing

## Technical Architecture

### Core Components

1. **Ensemble Model System**
   - **Model v7**: Title extraction for large documents (version 2)
   - **Model v14**: All-content processing for small documents
   - **Model v15**: All-content processing for large documents
   - **Model v20**: Specialized title extraction for large documents

2. **spaCy NER Pipeline**
   - **Token2Vec**: Advanced tokenization and vectorization
   - **NER Component**: Named Entity Recognition for "TITLE" entities
   - **Custom Training**: Trained on legal document corpus
   - **Performance Optimization**: Optimized for speed and accuracy

3. **OpenAI Integration**
   - **Embedding Model**: text-embedding-ada-002
   - **Similarity Matching**: Cosine similarity for title selection
   - **Semantic Understanding**: Context-aware title identification

### Model Performance Metrics

#### Model v20 (Latest - All Title Large)
```
Performance Metrics:
- F1-Score: 94.33%
- Precision: 95.39%
- Recall: 93.28%
- Training Steps: 20,000
- Vector Dimensions: 300
- Vocabulary Size: 514,157
```

#### Training Configuration
```
Optimization:
- Algorithm: Adam optimizer
- Learning Rate: 0.0001
- Dropout: 0.1
- Batch Size: Dynamic (100-1000 words)
- Gradient Clipping: 1.0
- L2 Regularization: 0.01
```

## Installation and Setup

### Prerequisites
- Python 3.8+
- spaCy 3.7.4+
- OpenAI API access
- Flask 3.0.3
- Required Python packages (see requirements.txt)

### Environment Variables
```bash
# OpenAI Configuration
openai_api_key=your_openai_api_key

# Model Configuration
MODEL_PATH=model/
ENSEMBLE_SIZE=4

# API Configuration
FLASK_PORT=5003
FLASK_DEBUG=True
```

### Installation Steps
1. **Clone Repository and Install Dependencies**:
   ```bash
   cd R_ml-model
   pip install -r requirements.txt
   ```

2. **Download Model Files**:
   - Ensure all four model directories are present in `/model/`
   - Models should be in `model-best/` subdirectories

3. **Configure Environment**:
   ```bash
   # Create .env file with OpenAI API key
   echo "openai_api_key=your_api_key_here" > .env
   ```

4. **Start the API Server**:
   ```bash
   python app.py
   ```

## API Documentation

### Endpoint: `/get_title`

**Purpose**: Extract titles from PDF pages using ensemble NER models

**Method**: `GET`
**Content-Type**: `application/json`
**Port**: `5003`

#### Request Format
```json
{
  "pdf_pages": [
    "Page 1 text content...",
    "Page 2 text content...",
    "Page 3 text content..."
  ],
  "file_name": "document_name.pdf"
}
```

#### Request Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `pdf_pages` | Array[String] | List of extracted text from each PDF page |
| `file_name` | String | Name of the PDF file being processed |

#### Response Format
```json
{
  "data": {
    "titles": [
      {
        "page_number": 1,
        "title": "EMPLOYMENT CONTRACT AGREEMENT",
        "confidence": 0.95,
        "model_consensus": 4
      },
      {
        "page_number": 3,
        "title": "TERMS AND CONDITIONS",
        "confidence": 0.87,
        "model_consensus": 3
      }
    ],
    "total_pages": 10,
    "pages_with_titles": 2,
    "processing_time": "2.34 seconds"
  },
  "status_code": 200,
  "message": "Success"
}
```

#### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `titles` | Array | List of extracted titles with metadata |
| `page_number` | Integer | Page number where title was found |
| `title` | String | Extracted title text |
| `confidence` | Float | Confidence score (0-1) |
| `model_consensus` | Integer | Number of models that agreed (0-4) |
| `total_pages` | Integer | Total number of pages processed |
| `pages_with_titles` | Integer | Number of pages containing titles |
| `processing_time` | String | Time taken to process the document |

#### Error Responses

**Empty Text Error (404)**:
```json
{
  "data": "",
  "status_code": 404,
  "message": "Empty Text"
}
```

**Processing Error (500)**:
```json
{
  "data": "Error details...",
  "status_code": 500,
  "message": "error"
}
```

## Model Architecture Details

### Ensemble Approach
The system uses four specialized models working in ensemble:

1. **Model v7 (Title Large 2)**
   - **Specialization**: Large document title extraction
   - **Training Focus**: Complex legal documents
   - **Performance**: Optimized for precision

2. **Model v14 (All New Small)**
   - **Specialization**: Small document processing
   - **Training Focus**: Concise legal documents
   - **Performance**: Fast processing, high recall

3. **Model v15 (All New Large)**
   - **Specialization**: Comprehensive large document analysis
   - **Training Focus**: Multi-page legal documents
   - **Performance**: Balanced precision and recall

4. **Model v20 (All Title Large)**
   - **Specialization**: Latest title extraction model
   - **Training Focus**: Enhanced title recognition
   - **Performance**: Highest F1-score (94.33%)

### NER Pipeline Components

#### Token2Vec Component
```python
# Tokenization and vectorization configuration
[components.tok2vec]
@architectures = "spacy.Tok2Vec.v2"
embed = ${components.tok2vec.embed}
encode = ${components.tok2vec.encode}
```

#### NER Component
```python
# Named Entity Recognition configuration
[components.ner]
@architectures = "spacy.TransitionBasedParser.v2"
state_type = "ner"
extra_state_tokens = false
hidden_width = 64
maxout_pieces = 2
use_upper = true
```

### Training Configuration
```python
# Training parameters
[training]
dropout = 0.1
patience = 1600-2000  # Varies by model
max_steps = 20000
eval_frequency = 200
batch_size = "compounding(100, 1000, 1.001)"
```

## Usage Examples

### 1. **Basic Title Extraction**
```python
import requests
import json

# Prepare request data
data = {
    "pdf_pages": [
        "LEASE AGREEMENT\n\nThis lease agreement is made between...",
        "TERMS AND CONDITIONS\n\n1. The tenant agrees to...",
        "Regular content without title..."
    ],
    "file_name": "lease_agreement.pdf"
}

# Send request
response = requests.get(
    'http://localhost:5003/get_title',
    json=data,
    headers={'Content-Type': 'application/json'}
)

# Process response
result = response.json()
if result['status_code'] == 200:
    titles = result['data']['titles']
    for title_info in titles:
        print(f"Page {title_info['page_number']}: {title_info['title']}")
```

### 2. **Batch Document Processing**
```python
import os
import requests
from pathlib import Path

def process_pdf_directory(pdf_directory):
    results = []

    for pdf_file in Path(pdf_directory).glob("*.pdf"):
        # Extract text from PDF (using your preferred PDF library)
        pdf_pages = extract_text_from_pdf(pdf_file)

        # Process with title extraction API
        data = {
            "pdf_pages": pdf_pages,
            "file_name": pdf_file.name
        }

        response = requests.get(
            'http://localhost:5003/get_title',
            json=data
        )

        results.append({
            'file': pdf_file.name,
            'result': response.json()
        })

    return results
```

### 3. **Integration with Document Management System**
```python
class DocumentTitleExtractor:
    def __init__(self, api_url="http://localhost:5003"):
        self.api_url = api_url

    def extract_titles(self, document_pages, filename):
        """Extract titles from document pages"""
        try:
            response = requests.get(
                f"{self.api_url}/get_title",
                json={
                    "pdf_pages": document_pages,
                    "file_name": filename
                },
                timeout=30
            )

            if response.status_code == 200:
                return response.json()['data']
            else:
                return None

        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return None

    def process_document_batch(self, documents):
        """Process multiple documents"""
        results = {}

        for doc_id, doc_data in documents.items():
            result = self.extract_titles(
                doc_data['pages'],
                doc_data['filename']
            )
            results[doc_id] = result

        return results
``` and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.com/lawpavilion-dev/machine-learning-model.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](https://gitlab.com/lawpavilion-dev/machine-learning-model/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.
