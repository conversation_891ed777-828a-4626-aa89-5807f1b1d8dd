# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from copy import deepcopy
from typing import Any

from azure.core import PipelineClient
from azure.core.rest import HttpRequest, HttpResponse

from . import models as _models
from ._configuration import AzureBlobStorageConfiguration
from ._serialization import Deserializer, Serializer
from .operations import (
    AppendBlobOperations,
    BlobOperations,
    BlockBlobOperations,
    ContainerOperations,
    PageBlobOperations,
    ServiceOperations,
)


class AzureBlobStorage:  # pylint: disable=client-accepts-api-version-keyword
    """AzureBlobStorage.

    :ivar service: ServiceOperations operations
    :vartype service: azure.storage.blob.operations.ServiceOperations
    :ivar container: ContainerOperations operations
    :vartype container: azure.storage.blob.operations.ContainerOperations
    :ivar blob: BlobOperations operations
    :vartype blob: azure.storage.blob.operations.BlobOperations
    :ivar page_blob: PageBlobOperations operations
    :vartype page_blob: azure.storage.blob.operations.PageBlobOperations
    :ivar append_blob: AppendBlobOperations operations
    :vartype append_blob: azure.storage.blob.operations.AppendBlobOperations
    :ivar block_blob: BlockBlobOperations operations
    :vartype block_blob: azure.storage.blob.operations.BlockBlobOperations
    :param url: The URL of the service account, container, or blob that is the target of the
     desired operation. Required.
    :type url: str
    :param base_url: Service URL. Required. Default value is "".
    :type base_url: str
    :keyword version: Specifies the version of the operation to use for this request. Default value
     is "2021-12-02". Note that overriding this default value may result in unsupported behavior.
    :paramtype version: str
    """

    def __init__(  # pylint: disable=missing-client-constructor-parameter-credential
        self, url: str, base_url: str = "", **kwargs: Any
    ) -> None:
        self._config = AzureBlobStorageConfiguration(url=url, **kwargs)
        self._client: PipelineClient = PipelineClient(base_url=base_url, config=self._config, **kwargs)

        client_models = {k: v for k, v in _models.__dict__.items() if isinstance(v, type)}
        self._serialize = Serializer(client_models)
        self._deserialize = Deserializer(client_models)
        self._serialize.client_side_validation = False
        self.service = ServiceOperations(self._client, self._config, self._serialize, self._deserialize)
        self.container = ContainerOperations(self._client, self._config, self._serialize, self._deserialize)
        self.blob = BlobOperations(self._client, self._config, self._serialize, self._deserialize)
        self.page_blob = PageBlobOperations(self._client, self._config, self._serialize, self._deserialize)
        self.append_blob = AppendBlobOperations(self._client, self._config, self._serialize, self._deserialize)
        self.block_blob = BlockBlobOperations(self._client, self._config, self._serialize, self._deserialize)

    def _send_request(self, request: HttpRequest, **kwargs: Any) -> HttpResponse:
        """Runs the network request through the client's chained policies.

        >>> from azure.core.rest import HttpRequest
        >>> request = HttpRequest("GET", "https://www.example.org/")
        <HttpRequest [GET], url: 'https://www.example.org/'>
        >>> response = client._send_request(request)
        <HttpResponse: 200 OK>

        For more information on this code flow, see https://aka.ms/azsdk/dpcodegen/python/send_request

        :param request: The network request you want to make. Required.
        :type request: ~azure.core.rest.HttpRequest
        :keyword bool stream: Whether the response payload will be streamed. Defaults to False.
        :return: The response of your network call. Does not do error handling on your response.
        :rtype: ~azure.core.rest.HttpResponse
        """

        request_copy = deepcopy(request)
        request_copy.url = self._client.format_url(request_copy.url)
        return self._client.send_request(request_copy, **kwargs)

    def close(self) -> None:
        self._client.close()

    def __enter__(self) -> "AzureBlobStorage":
        self._client.__enter__()
        return self

    def __exit__(self, *exc_details: Any) -> None:
        self._client.__exit__(*exc_details)
