import openai
import os
from dotenv import load_dotenv
import pandas as pd
load_dotenv()
from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
from milvus_config import *
import milvus_utils
import sys
import tiktoken
from flask import Flask, request, send_file,jsonify
from flask_cors import CORS
import json
import concurrent.futures
import time
from Prompt import classifier,denied,partially_resolved,more_info

class Email_Agent():

    def __init__(self) -> None:
        pass

    def gpt_classifier(self):
        msg =[
            {
            "role": "system",
            "content": [
                {
                "type": "text",
                "text": classifier.prompt2
                }
            ]
            },
            {
            "role": "user",
            "content": [
                {
                "type": "text",
                "text": str(self.query) 
                }
            ]
            }]
        response = openai.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=msg,
                    temperature=0.1,
                    max_tokens=30,
                    top_p=1,
                    frequency_penalty=0,
                    presence_penalty=0,
                    response_format={
                        "type": "json_object"
                    }
                    )
        # print(response)
        gpt_email_class = response.choices[0].message.content
        self.email_class = eval(gpt_email_class.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",","))


    def email_reply(self,query):
        self.query = query
        self.gpt_classifier()


    # def get_denied_reply(self):

    #     msg =[
    #         {
    #         "role": "system",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": denied.prompt
    #             }
    #         ]
    #         },
    #         {
    #         "role": "user",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": self.query 
    #             }
    #         ]
    #         }]
    #     response = openai.chat.completions.create(
    #                 model="gpt-4o",
    #                 messages=msg,
    #                 temperature=0.5,
    #                 max_tokens=1000,
    #                 top_p=1,
    #                 frequency_penalty=0,
    #                 presence_penalty=0
    #             )
    #     gpt_email_class = response.choices[0].message.content
    #     print(gpt_email_class)
    #     self.denied_reply = gpt_email_class.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",",")


    # def get_partially_denied_reply(self):

    #     msg =[
    #         {
    #         "role": "system",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": partially_resolved.prompt
    #             }
    #         ]
    #         },
    #         {
    #         "role": "user",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": self.query 
    #             }
    #         ]
    #         }]
    #     response = openai.chat.completions.create(
    #                 model="gpt-4o",
    #                 messages=msg,
    #                 temperature=0.5,
    #                 max_tokens=1000,
    #                 top_p=1,
    #                 frequency_penalty=0,
    #                 presence_penalty=0
    #             )
    #     gpt_email_class = response.choices[0].message.content
    #     print(gpt_email_class)
    #     self.denied_reply = gpt_email_class.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",",")

    # def get_more_information_asked_reply(self):

    #     msg =[
    #         {
    #         "role": "system",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": more_info.prompt
    #             }
    #         ]
    #         },
    #         {
    #         "role": "user",
    #         "content": [
    #             {
    #             "type": "text",
    #             "text": self.query 
    #             }
    #         ]
    #         }]
    #     response = openai.chat.completions.create(
    #                 model="gpt-4o",
    #                 messages=msg,
    #                 temperature=0.5,
    #                 max_tokens=1000,
    #                 top_p=1,
    #                 frequency_penalty=0,
    #                 presence_penalty=0
    #             )
    #     gpt_email_class = response.choices[0].message.content
    #     print(gpt_email_class)
    #     self.denied_reply = gpt_email_class.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",",")


