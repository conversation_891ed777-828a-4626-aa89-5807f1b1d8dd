import openai
import os
from dotenv import load_dotenv
import pandas as pd
load_dotenv()
from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
from milvus_config import *
import milvus_utils
import sys
import tiktoken
from flask import Flask, request, send_file,jsonify
from flask_cors import CORS
import json
import concurrent.futures
import time
from Email_Agent import Email_Agent


openai.api_key = os.environ.get('openai_api_key')
milvus_host = os.environ.get('MILVUS_HOST')
milvus_port = int(os.environ.get('MILVUS_PORT'))
conversation_token_limit = 14000
query_token_limit = 8000

completion_model = os.environ.get('GPT_COMPLETION')
embedding_engine = os.environ.get('GPT_EMBEDDINGS')

class DatabaseConnectionError(Exception):
    pass


app = Flask(__name__)
CORS(app)

# try:
#     app.sm = milvus_utils.MilvusSearchEngine(sm_table,sm_op_fields)
#     app.db1 = milvus_utils.MilvusSearchEngine(db1_table,db1_op_fields)
#     app.db2 = milvus_utils.MilvusSearchEngine(new_db2_table,new_db2_op_fields)
#     app.laws = milvus_utils.MilvusSearchEngine(law_table,laws_op_fields)
#     app.fna = milvus_utils.MilvusSearchEngine(fna_table,fna_op_fields)
#     app.lcrs = milvus_utils.MilvusSearchEngine(lfn_cpr_sub_reg_table,lfn_cpr_sub_reg_op_fields)
    
# except DatabaseConnectionError as e:
#     # print(f"Failed to initialize database connection: {str(e)}")
#     raise SystemExit(1)


def check_api_key(): 
    # print(os.getenv('api_key'))       
    if str(request.headers.get('Authorization')) != os.getenv('api_key'):
        return return_json(data=None,code=401, message="Unauthorized")
    else:
        return None 

def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:

    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens


def return_json(data,code,message):
    return {'data':data,'code':code,'message':message}

def restructure_email_thread(payload):
    mail_thread = {}
    payload = eval(payload)
    for mail in payload:
        # print(mail)
        for sender, content in mail.items():
            
            mail_content = f'''
Subject: {content['subject']}
Body:
{content['body']}
            
'''

            mail_thread[sender] = mail_content
    return mail_thread

@app.route('/ai_email',methods = ['POST'])
def get_reply():

    auth_result = check_api_key()
    if auth_result:
        return auth_result
    
    try:

        query = str(request.get_json())
        query = restructure_email_thread(query)
        # print(query)
        mail_agent = Email_Agent()

        mail_agent.email_reply(query)

        print(mail_agent.email_class['category'] )

        return jsonify(return_json({
                "mail_status":mail_agent.email_class['category']               
                },
                200,
                'Request Successful'))


    except openai.RateLimitError as e:

       
        return jsonify(return_json({
                "mail_status":None
                },
                429,
                'Please check openai payment'))

    
    except Exception as e:
        error_message = f"Error on line {sys.exc_info()[-1].tb_lineno}: {str(e)}"
        return jsonify(return_json(error_message,500,'Failed'))


 


if __name__ == '__main__':
    app.run(debug=True, port=5005)