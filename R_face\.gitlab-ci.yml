before_script:
  - apt-get update -qq
  - apt-get install -qq git
  # Setup SSH deploy keys
  - 'which ssh-agent || ( apt-get install -qq openssh-client )'
  - eval $(ssh-agent -s)
  - ssh-add <(echo "$SSH_PRIVATE_KEY")
  - mkdir -p ~/.ssh
  - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'


# deploy_release:
#   stage: deploy
#   environment:
#     name: stage
#   script:
#     - echo "Deploying Facial Recognition to the server"
#     - git branch -v
#     - git remote add dokku dokku@**************:facial-recognition
#     - git push -f dokku HEAD:refs/heads/master
#   only:
#     - main

deploy_release:
  stage: deploy
  environment:
    name: production
  script:
    - echo "Deploying Face Recognition to the server"
    - ssh root@************* "cd /home/<USER>/face-recognition-app && git checkout main && git pull origin main && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
  only:
    - main

