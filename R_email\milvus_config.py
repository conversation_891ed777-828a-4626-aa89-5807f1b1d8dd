######################

sm_table = 'SUBJECT_MATTER_FILTERED'
sm_op_fields=['COURT_NAME', 'SUBJECT_MATTER', 'MODE_OF_COMMENCEMENT']

######################

hc_table = 'HIGH_COURT_CLEAN'
hc_op_fields = ['ISSUES','SUITNUMBER','CASETITLE']

######################

db1_table = 'DB1'
db1_op_fields = ['case_title','suitno','citation','issues','story']

######################

db2_table = 'DB2'
db2_op_fields = ['case_title','suitno','citation','issues','principle','judge','references']

#######################

law_table = 'laws_lfn_rules'
laws_op_fields = ['title','year','section_number','subsection_number','content','source']

#######################

lfn_cpr_sub_reg_table = 'new_lfn_cpr_sub_reg'
lfn_cpr_sub_reg_op_fields =[ 'content', 'title', 'source', 'year', 'description', 'number', 
           'sub_number', 'rule_id', 'acronym', 'imagetext', 'fullname']

#######################

fna_table = 'forms_and_agreements'
fna_op_fields = ['title','type']

#######################
new_db2_table = 'new_DB2'
new_db2_op_fields = ['case_title', 'suitno', 'citation', 'issues', 'principle', 'judge','references', 'court', 'ratio_pk', 'clickable_references']