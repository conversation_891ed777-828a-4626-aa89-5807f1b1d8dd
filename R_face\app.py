#################################################################

from flask import Flask, render_template, request, send_file,jsonify
import cv2
import dlib
import os
import numpy as np
import natsort
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from PIL import Image
import random
from sqlalchemy import create_engine
import mysql.connector

# from config import OUTPUT_DIR,USERNAME,PASSWORD,HOST,DATABASE,TABLE,DOMAIN

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

#################################################################


app = Flask(__name__)

# Access the environment variables

OUTPUT_DIR = os.environ.get('OUTPUT_DIR')
DOMAIN = os.environ.get('DOMAIN')
USERNAME = os.environ.get('USERNAME_SQL')
PASSWORD = os.environ.get('PASSWORD')
HOST = os.environ.get('HOST_SQL')
DATABASE = os.environ.get('DATABASE')
TABLE = os.environ.get('TABLE')

@app.route('/upload', methods=['POST'])
def upload():
    create_table()

    # Get the uploaded video file
    file = request.files['file']

    

    # Get detail from user
    # get_report_id
    report_id = str(request.form.get('report id'))

    # get_report_type
    report_type = str(request.form.get('report type'))

    # get_location
    video_location = str(request.form.get('location'))


    file_name = f"{file.filename}"
    print(file_name)
    image_format = ('.jpg','.jpeg','webp','.png')#,'.heic','.HEIC')

    # Save the video file temporarily
    if file_name.endswith('.mp4'):
        video_path = 'temp_video.mp4'
        file.save(video_path)

        # Process the video and get the number of frames
        # num_frames = count_frames(video_path)
        file_get_single_faces(video_path,
                              r_id=report_id,
                              r_type=report_type,
                              location = video_location,
                              file_name = file_name
                              )



    # Remove the temporary video file
        remove_temp_file(video_path)

    # Return the number of frames
        return f"Faces in {file_name} uploaded to database"
    
    if file_name.endswith(image_format):
        
        image_path = f"temp_image.{file_name.split('.')[-1]}"
        file.save(image_path)

        # text = get_info(image_path)
        file_get_single_faces(image_path,
                              r_id=report_id,
                              r_type=report_type,
                              location = video_location,
                              file_name = file_name
                              )



        remove_temp_file(image_path)
        
        return f"Faces in {file_name} uploaded to database"


        # pixel = count_pixel(image_path)
        # remove_temp_file(image_path)


        # return f'Total number of pixels: {pixel}'

@app.route('/info', methods=['GET', 'POST'])
def give_info():
    file = request.files['file']
    file_name = f"{file.filename}"
    print(file_name)
    image_format = ('.jpg','.jpeg','webp','.png')#,'.heic','.HEIC')
    if file_name.endswith(image_format):
        image_name = f'{file.filename}'
        image_path = f"temp_image.{image_name.split('.')[-1]}"
        file.save(image_path)

        text = get_info(image_path)

        remove_temp_file(image_path)
        print(text)
        # return ''.join(text)
        return jsonify(text)

@app.route('/frequent',methods = ['GET'])
def frequent():
    threshold = int(request.args.get('threshold')) # change
    
    face_list = get_freq_faces(threshold)
    print(face_list)
    if len(face_list) == 0:
        return f"No face occured {threshold} times"
    # print(face_list)
    face_str = ','.join([str(x) for x in face_list])

    return f"Faces {face_str} have occured more than {threshold}."

@app.route('/top_faces', methods=['GET'])
def get_images():
    # image_folder = os.path.join(os.getcwd(), 'images')
    image_folder = f'{OUTPUT_DIR}/'
    image_files = os.listdir(image_folder)  
    k = int(request.args.get('top_k_faces'))  # change

    top_face_list = top_faces(k)    #chamge
    # Faces Recorded/1.jpg
    print(top_face_list)
    # image_urls = [image_folder + str(face)+'.jpg' for face in top_face_list]

    # return jsonify(image_urls)
    image_urls = []
    for image_file in top_face_list:
        image_url = f"{DOMAIN}/top_faces/{image_file}.jpg"
        image_urls.append(image_url)
        print(image_url)

    return jsonify(image_urls)

@app.route('/top_faces/<path:image_file>')
def get_image_url(image_file):
    # Specify the path to the folder containing the images
    image_folder = f'{OUTPUT_DIR}'#'Faces Recorded'

    # Get the full path of the requested image file
    image_path = os.path.join(image_folder, image_file)
    
    return send_file(image_path, mimetype='image/jpeg')
    

#################################################################
def remove_temp_file(file_path):
    # Remove the temporary file
    import os
    os.remove(file_path)

def get_encodings(frame,faces):
    predictor = dlib.shape_predictor("models/shape_predictor_68_face_landmarks.dat")

    # Load face recognition model from dlib
    facerec = dlib.face_recognition_model_v1("models/dlib_face_recognition_resnet_model_v1.dat")
    shape = predictor(frame, faces)
    face_descriptor = facerec.compute_face_descriptor(frame, shape)
    # print(face_descriptor,len(face_descriptor))
    # Convert face descriptor to a numpy array
    return np.array(face_descriptor)

def get_info(file_path):

    # image_format = ('.jpg','.webp','.png')

    detector = dlib.get_frontal_face_detector()
    

    df = read_mysql_to_df(TABLE)

    # print(df)
    all_names = [str(name) for name in df['name'].tolist()]
    # print(all_names)


    # df['videos'] = df['videos'].astype(str)
    # df['videos'] = df['videos'].str.strip('[]').str.split(', ')
    df['videos'] = df['videos'].str.split(', ')

    video_list = df['videos'].tolist()

    # print(video_list)

    found_in_video = dict(zip(all_names, df['videos'].tolist()))
    # print(found_in_video)

    all_encodings = df['encodings'].apply(eval).apply(np.array).to_list()
    # print(all_encodings)

    # df['count'] = df['count'].str.split(', ')
    all_count = dict(zip(all_names, df['count'].to_list()))
    # print(all_count)

    # df['video_count'] = df['video_count'].str.split(', ')
    video_count = dict(zip(all_names, df['video_count'].to_list()))
    # print(video_count)

    df['report_id'] = df['report_id'].str.split(', ')
    r_id_list = dict(zip(all_names, df['report_id'].to_list()))
    # print(r_id_list)

    df['report_type'] = df['report_type'].str.split(', ')
    r_type_list = dict(zip(all_names, df['report_type'].to_list()))
    # print(r_type_list)

    df['location'] = df['location'].str.split(', ')
    location_list = dict(zip(all_names, df['location'].to_list()))
    # print(location_list)

    closest_list =  dict(zip(all_names, df['closest_encoding'].apply(eval).apply(np.array).to_list()))
    # print(closest_list)

    text = ''
    text_dict = {}
    face_distance_threshold = .47
    frame_scale = 2.5

    frame = cv2.imread(file_path)

    # print(file_path)

    frame = cv2.resize(frame, (0, 0), fx= 1/frame_scale, fy=1/frame_scale)
            
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) 
    
    gray = cv2.convertScaleAbs(gray, alpha=1, beta=0)

    faces = detector(gray,0)

    not_found = True

    # Iterate through detected faces in the frame
    for i,face in enumerate(faces):

        encoding = get_encodings(frame,face)
        
        for j in range(len(all_encodings)):
            dist = np.linalg.norm(encoding - all_encodings[j])

            if dist < face_distance_threshold:
                not_found = False

                face_name =  f"{all_names[j]}"
                # print(face_name)
                face_video_count = video_count[face_name]
                face_location = location_list[face_name]
                face_r_id = r_id_list[face_name]
                videos = found_in_video[face_name]


                face_r_type = r_type_list[face_name]
                save_image = cv2.resize(frame[face.top():face.bottom(), face.left():face.right()], (0, 0), fx=frame_scale, fy=frame_scale)


                text = (f" The face Id {face_name} is found in {face_video_count} videos.",
                        f"\n Report IDs: {face_r_id}",
                        f"\n Report Types: {face_r_type}",
                        f"\n Number of crimes reported: {face_r_type.count('crime')}",
                        f"\n Number of violations reported: {face_r_type.count('violation')}",
                        f"\n Was Seen at these locations: {face_location}"
                        )
                
                text_dict = {
                    "message":f'The face Id {face_name} is found in {face_video_count} videos.',
                    "Report IDs":face_r_id,
                    "Report Types":face_r_type,
                    "Number of crimes reported":face_r_type.count('crime'),
                    "Number of violations reported":face_r_type.count('violation'),
                    "Was Seen at these locations": face_location,
                    "Files" : videos

                }
                # text_dict = {
                #         "message:: The face Id 8 is found in 3 videos.
                #         "Report IDs": ['104', '112', '113']
                #         "Report Types": ['violation', 'violation', 'violation']
                #         "Number of crimes reported": 0
                #         "Number of violations reported": 3
                #         "Was Seen at these locations": ['40.24.14.8.N, 43.10.25.7.E', '43.24.9.5.N, 35.10.23.2.E', '42.24.12.3.N, 17.10.28.2.E']
                #     }
                
    
    if not_found:
        text = ('New Face')
        print('This face is new.')
        return text
    else:
        return text_dict

    
def get_coordinate():
    # Convert the given coordinates to decimal degrees
    lat = random.randint(10,50) + 24/60 + 12.2/3600
    lon = random.randint(10,50)+ 10/60 + 26.5/3600
    
    # Add a random offset to the decimal degrees
    lat += random.uniform(-0.001, 0.001)
    lon += random.uniform(-0.001, 0.001)
    
    # Convert back to degrees, minutes, seconds format
    lat_degrees = int(lat)
    lat_minutes = int((lat - lat_degrees) * 60)
    lat_seconds = (lat - lat_degrees - lat_minutes/60) * 3600
    lat_direction = "N" if lat >= 0 else "S"
    
    lon_degrees = int(lon)
    lon_minutes = int((lon - lon_degrees) * 60)
    lon_seconds = (lon - lon_degrees - lon_minutes/60) * 3600
    lon_direction = "E" if lon >= 0 else "W"
    
    # Format the output string
    output = "{:02d}.{:02d}.{:.1f}.{} {:02d}.{:02d}.{:.1f}.{}".format(
        abs(lat_degrees), lat_minutes, lat_seconds, lat_direction,
        abs(lon_degrees), lon_minutes, lon_seconds, lon_direction
    )
    
    return output

def get_report_type():
    terms = ['crime', 'violation']
    return random.choice(terms)

def get_report_id():
    random_number = random.randint(1, 999999)
    formatted_number = str(random_number).zfill(4)
    return f'{formatted_number}'

def file_get_single_faces(file_path,r_id,r_type,location,file_name):

    detector = dlib.get_frontal_face_detector()
    ##################################################################  Initial folder

    output_dir = OUTPUT_DIR #'Faces Recorded'

    if not os.path.exists(output_dir):
        os.mkdir(output_dir)

    ##################################################################  Initial Variable
    face_distance_threshold = 0.47
    frame_scale = 2.5
    image_format = ('.jpg','.jpeg','webp','.png')#,'.heic','.HEIC')
    ##################################################################  Read DF and Save

    csv_path = 'Faces.csv'

    frame_skip = 24


    df = read_mysql_to_df(TABLE)
    # print(df)
    # print(df)
    all_names = [str(name) for name in df['name'].tolist()]
    # print(all_names)


    # df['videos'] = df['videos'].astype(str)
    # df['videos'] = df['videos'].str.strip('[]').str.split(', ')
    df['videos'] = df['videos'].str.split(', ')

    video_list = df['videos'].tolist()

    # print(video_list)

    found_in_video = dict(zip(all_names, df['videos'].tolist()))
    # print(found_in_video)

    all_encodings = df['encodings'].apply(eval).apply(np.array).to_list()
    # print(all_encodings)

    # df['count'] = df['count'].str.split(', ')
    all_count = dict(zip(all_names, df['count'].to_list()))
    # print(all_count)

    # df['video_count'] = df['video_count'].str.split(', ')
    video_count = dict(zip(all_names, df['video_count'].to_list()))
    # print(video_count)

    df['report_id'] = df['report_id'].str.split(', ')
    r_id_list = dict(zip(all_names, df['report_id'].to_list()))
    # print(r_id_list)

    df['report_type'] = df['report_type'].str.split(', ')
    r_type_list = dict(zip(all_names, df['report_type'].to_list()))
    # print(r_type_list)

    df['location'] = df['location'].str.split(', ')
    location_list = dict(zip(all_names, df['location'].to_list()))
    # print(location_list)

    closest_list =  dict(zip(all_names, df['closest_encoding'].apply(eval).apply(np.array).to_list()))
    # print(closest_list)

    if file_name.endswith('.mp4'):


        video_name = file_name.split('.')[0]

        print(video_name)

        cap_video = cv2.VideoCapture(file_path)

        frame_count = 0

        while cap_video.isOpened():
            ret, frame = cap_video.read()

            if not ret:
                break

            frame_count += 1

            if frame_count % frame_skip ==0 :
                frame = cv2.resize(frame, (0, 0), fx= 1/frame_scale, fy=1/frame_scale)
                # frame_copy = frame.copy()

                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) 

                gray = cv2.convertScaleAbs(gray, alpha=1, beta=0)

                faces = detector(gray,0)

                for i,face in enumerate(faces):

                    encoding = get_encodings(frame,face)

                    is_unique = True 

                    for j in range(len(all_encodings)):
                        face_distance = np.linalg.norm(encoding - all_encodings[j])

                        if face_distance < face_distance_threshold:

                            is_unique = False

                            print(j)

                            

                            all_count[all_names[j]] += 1


                            n_count = all_count[all_names[j]]

                            name = f"{all_names[j]}"

                            print(name)

                            image_path = os.path.join(output_dir, f"{name}.jpg")
                            print(image_path)
                            if video_name not in found_in_video[name]:
                                found_in_video[name].append(video_name)
                                video_count[name] = len(found_in_video[name]) ###########
                                r_id_list[name].append(r_id)
                                r_type_list[name].append(r_type)
                                location_list[name].append(location)
        
                            
                            try:

                                
                                all_encodings[j] = [(encoding[i] + all_encodings[j][i]* (n_count-1) ) / n_count for i in range(len(encoding))]

                                diff_sum_encoding = np.linalg.norm(encoding - all_encodings[j])

                                diff_sum_closest =np.linalg.norm(closest_list[name] - all_encodings[j])

                                if diff_sum_encoding < diff_sum_closest:
                                    closest_list[name] = encoding
                                    save_image = cv2.resize(frame[face.top():face.bottom(), face.left():face.right()], (0, 0), fx=frame_scale, fy=frame_scale)
                                    stretch_near = cv2.resize(save_image, (780, 770),interpolation = cv2.INTER_LINEAR)
                                    cv2.imwrite(image_path, stretch_near)
                                    # print(f"{all_count[all_names[j]]} __ {diff_sum_encoding} __ {diff_sum_closest}")

                            except:
                                pass
                            break
                    
                    if is_unique :

                        name = f"{len(all_names) + 1}"
                        print('New')

                        found_in_video[name] = [video_name]
                        video_count[name] = len(found_in_video[name])###########
                        r_id_list[name] = [r_id]
                        r_type_list[name] = [r_type]
                        location_list[name] = [location]
                        closest_list[name] = encoding

                        all_count[name] = 1

                        image_path = os.path.join(output_dir, f"{name}.jpg")
                        print(image_path)
                        
                        try:
                            save_image = cv2.resize(frame[face.top():face.bottom(), face.left():face.right()], (0, 0), fx=frame_scale, fy=frame_scale)
                            stretch_near = cv2.resize(save_image, (780, 770),interpolation = cv2.INTER_LINEAR)
                            cv2.imwrite(image_path, stretch_near)
                        
                            all_encodings.append(encoding)

                            all_names.append(name)
                        except:
                            pass

    
    if file_name.endswith(image_format):
        face_distance_threshold = .44
        frame_scale = .8
        frame = cv2.imread(file_path)
        print(file_path)

        frame = cv2.resize(frame, (0, 0), fx= 1/frame_scale, fy=1/frame_scale)

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) 

        gray = cv2.convertScaleAbs(gray, alpha=1, beta=0)

        faces = detector(gray,0)

        image_name = file_name.split('.')[0]

        # Iterate through detected faces in the frame
        # print(faces)
        # print(faces)
        for i,face in enumerate(faces):
            
            
            encoding = get_encodings(frame,face)

            # Check if face is unique based on distance from previous faces
            is_unique = True
            # print(len(all_encodings))
            for j in range(len(all_encodings)):
                dist = np.linalg.norm(encoding - all_encodings[j])
                # print(dist,face_distance_threshold)
                # print(j)
                if dist < face_distance_threshold:
                    # Face is not unique
                    is_unique = False
                    # Increment count for this face
                    all_count[all_names[j]] += 1
                    n_count = all_count[all_names[j]]
                    name = f"{all_names[j]}"
                    # print(name)


                    if image_name not in found_in_video[name]:
                        found_in_video[name].append(image_name)
                        video_count[name] = len(found_in_video[name]) ###########
                        r_id_list[name].append(r_id)
                        r_type_list[name].append(r_type)
                        location_list[name].append(location)

                    
                    image_path = os.path.join(output_dir, f"{name}.jpg")
                    print(image_path)
                    try:
                        

                        all_encodings[j] = [(encoding[i] * (n_count-1) + all_encodings[j][i]) / n_count for i in range(len(encoding))]
                        diff_sum_encoding = np.linalg.norm(encoding - all_encodings[j])

                        diff_sum_closest =np.linalg.norm(closest_list[name] - all_encodings[j])

                        if diff_sum_encoding < diff_sum_closest:
                            closest_list[name] = encoding
                            save_image = cv2.resize(frame[face.top():face.bottom(), face.left():face.right()], (0, 0), fx=frame_scale, fy=frame_scale)
                            stretch_near = cv2.resize(save_image, (780, 770),interpolation = cv2.INTER_LINEAR)
                            cv2.imwrite(image_path, stretch_near)
                    except:
                        all_count[all_names[j]] -= 1 
                    break

            # If face is unique, add it to list of unique faces
            if is_unique:
                # Generate new folder for this face if it doesn't exist
                name = f"{len(all_names) + 1}"
                

                found_in_video[name] = [image_name]
                video_count[name] = len(found_in_video[name])###########
                r_id_list[name] = [r_id]
                r_type_list[name] = [r_type]
                location_list[name] = [location]
                closest_list[name] = encoding


                # # Save image of the face to the folder
                all_count[name] = 1
                image_path = os.path.join(output_dir, f"{name}.jpg")
                try:
                    save_image = cv2.resize(frame[face.top():face.bottom(), face.left():face.right()], (0, 0), fx=frame_scale, fy=frame_scale)
                    stretch_near = cv2.resize(save_image, (780, 770),interpolation = cv2.INTER_LINEAR)
                    cv2.imwrite(image_path, stretch_near)
                            
                    all_encodings.append(encoding)
                    all_names.append(name)      
                except:
                    pass

    # video_list = ','.join(found_in_video.values())


    df1 = pd.DataFrame(columns=['name','count','videos','video_count','report_id','report_type','location','closest_encoding','encodings'])
    # df1 = pd.DataFrame(df.columns)
    df1['name'] = all_names
    df1['videos'] = list(found_in_video.values())
    df1['videos'] = df1['videos'].apply(lambda x: ', '.join(x))

    print(df1['videos'])
    df1['encodings'] = all_encodings
    df1['encodings'] = df1['encodings'].apply(lambda x: ','.join(map(str, x)))
    
    df1['count'] = list(all_count.values())
    df1['video_count'] = list(video_count.values())
    df1['report_id'] = list(r_id_list.values())
    df1['report_id'] = df1['report_id'].apply(lambda x: ', '.join(x))

    df1['report_type'] = list(r_type_list.values())
    df1['report_type'] = df1['report_type'].apply(lambda x: ', '.join(x))

    df1['location'] = list(location_list.values())
    df1['location'] = df1['location'].apply(lambda x: ', '.join(x))

    df1['closest_encoding'] = list(closest_list.values())
    df1['closest_encoding'] = df1['closest_encoding'].apply(lambda x: ','.join(map(str, x)))
    # df1.head()
    save_df_to_mysql(df1,TABLE)
        
def get_freq_faces(threshold):
    
    df = read_mysql_to_df(TABLE)

    df_freq = df[df['video_count'] >= threshold]

    return df_freq['name'].tolist()

def top_faces(top_k_faces):
    
    df = read_mysql_to_df(TABLE)

    df_freq  = df.sort_values(by='video_count', ascending=False)
    df_freq = df_freq.head(top_k_faces)

    return df_freq['name'].tolist()


def read_mysql_to_df(table_name):
    # Connect to the MySQL database
    # dialect+driver://username:password@host:port/database
    engine = create_engine(f'mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}/{DATABASE}')
    # engine = create_engine('mysql+pymysql://root:root@localhost/face_schema')
    print(f'mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}/{DATABASE}')
    # Query the MySQL table and retrieve the data into a DataFrame
    df = pd.read_sql('SELECT * FROM ' + table_name, con=engine)
    
    # Close the database connection
    engine.dispose()
    
    return df

def save_df_to_mysql(df, table_name):
    # Connect to the MySQL database
    # engine = create_engine('mysql+pymysql://root:root@localhost/face_schema')
    engine = create_engine(f'mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}/{DATABASE}')
    
    # Save the DataFrame to a MySQL table
    df.to_sql(table_name, con=engine, if_exists='replace', index=False)
    
    # Close the database connection
    engine.dispose()


def create_table():
    cnx = mysql.connector.connect(
        host=HOST,
        user=USERNAME,
        password=PASSWORD,
        database=DATABASE
    )

    # Create a cursor object to interact with the database
    cursor = cnx.cursor()

    # Create the table if it doesn't exist
    create_table_query = f'''
        CREATE TABLE IF NOT EXISTS {TABLE} (
            name TEXT,
            count bigint,
            videos TEXT,
            video_count bigint,
            report_id TEXT,
            report_type TEXT,
            location TEXT,
            closest_encoding TEXT,
            encodings TEXT
        )
    '''
    cursor.execute(create_table_query)
#################################################################

if __name__ == '__main__':

    
    app.run(debug=True)
