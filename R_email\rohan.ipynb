{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'user': 'Subject: Thank you note\\nBody:Thanks for the update. Can you provide an estimated delivery date?',\n", " 'agency': 'Subject: Response\\nBody:You will get your delivery on next Monday EOD'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["def restructure_email_thread(payload):\n", "    mail_thread = {}\n", "    payload = eval(payload)\n", "    # print(payload)\n", "    for mail in payload:\n", "        for sender, content in mail.items():\n", "            \n", "            mail_content = f'''Subject: {content['subject']}\n", "Body:{content['body']}'''\n", "            mail_thread[sender] = mail_content\n", "\n", "    return mail_thread\n", "payload = [\n", "  {\n", "    \"user\": {\n", "      \"id\": 1,\n", "      \"sender_type\": \"user\",\n", "      \"subject\": \"Question about my order\",\n", "      \"body\": \"Can you tell me the status of my order #1234?\"\n", "    }\n", "  },\n", "  {\n", "    \"agency\": {\n", "      \"id\": 2,\n", "      \"sender_type\": \"agency\",\n", "      \"subject\": \"Response to user Question\",\n", "      \"body\": \"Hello, we are currently processing your order. We will update you shortly.\"\n", "    }\n", "  },\n", "  {\n", "    \"user\": {\n", "      \"id\": 1,\n", "      \"sender_type\": \"user\",\n", "      \"subject\": \"Thank you note\",\n", "      \"body\": \"Thanks for the update. Can you provide an estimated delivery date?\"\n", "    }\n", "  },\n", "  {\n", "    \"agency\": {\n", "      \"id\": 2,\n", "      \"sender_type\": \"agency\",\n", "      \"subject\": \"Response\",\n", "      \"body\": \"You will get your delivery on next Monday EOD\"\n", "      }\n", "    },\n", "]\n", "restructure_email_thread(str(payload))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}