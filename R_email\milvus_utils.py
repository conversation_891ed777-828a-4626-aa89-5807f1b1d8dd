import openai
import os
from dotenv import load_dotenv
import pandas as pd
load_dotenv()
from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility


openai.api_key = os.environ.get('openai_api_key')
milvus_host = os.environ.get('MILVUS_HOST')
milvus_port = int(os.environ.get('MILVUS_PORT'))

class MilvusSearchEngine:
    def __init__(self, collection_name,output_field):
        self.HOST = milvus_host
        self.PORT = milvus_port
        self.DIMENSION = 1536
        self.COLLECTION_NAME = collection_name
        self.OUTPUTFIELD = output_field
        # self.fields = fields
        self.INDEX_PARAM = {
            'metric_type': 'L2',
            'index_type': 'HNSW',
            'params': {'M': 8, 'efConstruction': 64}
        }
        self.QUERY_PARAM = {
            "metric_type": "L2",
            "params": {"ef": 64},
        }
        self._connect_to_collection()

    def _connect_to_collection(self):
        connections.connect(host=self.HOST, port=self.PORT)
        self.collection = Collection(name=self.COLLECTION_NAME)
        self.collection.load()

    def search(self, question,top_k):
        embedding_response = openai.embeddings.create(
            input=question,
            model="text-embedding-ada-002"
        )


        q_embeddings  = embedding_response.data[0].embedding


        if q_embeddings is None:
            # Handle the case where 'embedding' is not present in the response
            raise ValueError("Embedding not found in response.")
        res = self.collection.search([q_embeddings], 
                                     anns_field='embedding', 
                                     param=self.QUERY_PARAM, 
                                     limit=top_k,
                                     output_fields=self.OUTPUTFIELD)
        data_list = []

        # Iterate through the search results and append the data to the list
        for hit in res:
            for hits in hit:
                data = {field: getattr(hits.entity, field, None) for field in self.OUTPUTFIELD}
                data_list.append(data)

        # Create a DataFrame from the list of dictionaries
        return pd.DataFrame(data_list)
    

    def source_search(self, question,source,top_k):
        embedding_response = openai.embeddings.create(
            input=question,
            model="text-embedding-ada-002"
        )


        q_embeddings  = embedding_response.data[0].embedding


        if q_embeddings is None:
            # Handle the case where 'embedding' is not present in the response
            raise ValueError("Embedding not found in response.")
        res = self.collection.search([q_embeddings], 
                                     anns_field='embedding', 
                                     param=self.QUERY_PARAM, 
                                     limit=top_k,
                                     output_fields=self.OUTPUTFIELD,
                                     expr = f""" source in {[str(source)]}""")
        data_list = []

        # Iterate through the search results and append the data to the list
        for hit in res:
            for hits in hit:
                data = {field: getattr(hits.entity, field, None) for field in self.OUTPUTFIELD}
                data['distance'] = hits.distance
                data_list.append(data)

        # Create a DataFrame from the list of dictionaries
        return pd.DataFrame(data_list)
    
    
    def query(self,expression,top_k):
        res = self.collection.query(
        expr= expression,
        output_fields=self.OUTPUTFIELD,
        limit = top_k,
        )
        return pd.DataFrame(res)
    
    def delete(self,id:list):
        res = self.collection.delete(
            expr =f"id in {str(id)}"
        )   
        return res
    
    def delete_by_filter(self,id):
        res = self.collection.delete(
            filter=f"id in {str(id)}"
        )   
        return res