Metadata-Version: 2.1
Name: argon2-cffi
Version: 23.1.0
Summary: Argon2 for Python
Project-URL: Documentation, https://argon2-cffi.readthedocs.io/
Project-URL: Changelog, https://github.com/hynek/argon2-cffi/blob/main/CHANGELOG.md
Project-URL: GitHub, https://github.com/hynek/argon2-cffi
Project-URL: Funding, https://github.com/sponsors/hynek
Project-URL: Tidelift, https://tidelift.com/?utm_source=lifter&utm_medium=referral&utm_campaign=hynek
Author-email: Hynek <PERSON>hlawack <<EMAIL>>
License-Expression: MIT
License-File: LICENSE
Keywords: hash,hashing,password,security
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Security :: Cryptography
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Requires-Dist: argon2-cffi-bindings
Requires-Dist: typing-extensions; python_version < '3.8'
Provides-Extra: dev
Requires-Dist: argon2-cffi[tests,typing]; extra == 'dev'
Requires-Dist: tox>4; extra == 'dev'
Provides-Extra: docs
Requires-Dist: furo; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: sphinx; extra == 'docs'
Requires-Dist: sphinx-copybutton; extra == 'docs'
Requires-Dist: sphinx-notfound-page; extra == 'docs'
Provides-Extra: tests
Requires-Dist: hypothesis; extra == 'tests'
Requires-Dist: pytest; extra == 'tests'
Provides-Extra: typing
Requires-Dist: mypy; extra == 'typing'
Description-Content-Type: text/markdown

# *argon2-cffi*: Argon2 for Python


[Argon2](https://github.com/p-h-c/phc-winner-argon2) won the [Password Hashing Competition](https://www.password-hashing.net/) and *argon2-cffi* is the simplest way to use it in Python:

```pycon
>>> from argon2 import PasswordHasher
>>> ph = PasswordHasher()
>>> hash = ph.hash("correct horse battery staple")
>>> hash  # doctest: +SKIP
'$argon2id$v=19$m=65536,t=3,p=4$MIIRqgvgQbgj220jfp0MPA$YfwJSVjtjSU0zzV/P3S9nnQ/USre2wvJMjfCIjrTQbg'
>>> ph.verify(hash, "correct horse battery staple")
True
>>> ph.check_needs_rehash(hash)
False
>>> ph.verify(hash, "Tr0ub4dor&3")
Traceback (most recent call last):
  ...
argon2.exceptions.VerifyMismatchError: The password does not match the supplied hash

```
<!-- end short -->

## Project Links

- [**PyPI**](https://pypi.org/project/argon2-cffi/)
- [**GitHub**](https://github.com/hynek/argon2-cffi)
- [**Documentation**](https://argon2-cffi.readthedocs.io/)
- [**Changelog**](https://github.com/hynek/argon2-cffi/blob/main/CHANGELOG.md)
- [**Funding**](https://hynek.me/say-thanks/)
- The low-level Argon2 CFFI bindings are maintained in the separate [*argon2-cffi-bindings*](https://github.com/hynek/argon2-cffi-bindings) project.

## Release Information

### Removed

- Python 3.6 is not supported anymore.


### Deprecated

- The `InvalidHash` exception is deprecated in favor of `InvalidHashError`.
  No plans for removal currently exist and the names can (but shouldn't) be used interchangeably.

- `argon2.hash_password()`, `argon2.hash_password_raw()`, and `argon2.verify_password()` that have been soft-deprecated since 2016 are now hard-deprecated.
  They now raise `DeprecationWarning`s and will be removed in 2024.


### Added

- Official support for Python 3.11 and 3.12.
  No code changes were necessary.

- `argon2.exceptions.InvalidHashError` as a replacement for `InvalidHash`.

- *salt* parameter to `argon2.PasswordHasher.hash()` to allow for custom salts.
  This is only useful for specialized use-cases -- leave it on None unless you know exactly what you are doing.
  [#153](https://github.com/hynek/argon2-cffi/pull/153)


---

[→ Full Changelog](https://github.com/hynek/argon2-cffi/blob/main/CHANGELOG.md)


## Credits

*argon2-cffi* is maintained by [Hynek Schlawack](https://hynek.me/).

The development is kindly supported by my employer [Variomedia AG](https://www.variomedia.de/), *argon2-cffi* [Tidelift subscribers](https://tidelift.com/?utm_source=lifter&utm_medium=referral&utm_campaign=hynek), and my amazing [GitHub Sponsors](https://github.com/sponsors/hynek).


## *argon2-cffi* for Enterprise

Available as part of the Tidelift Subscription.

The maintainers of *argon2-cffi* and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open-source packages you use to build your applications.
Save time, reduce risk, and improve code health, while paying the maintainers of the exact packages you use.
[Learn more.](https://tidelift.com/?utm_source=lifter&utm_medium=referral&utm_campaign=hynek)
