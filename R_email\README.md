# Email AI - Complaint Classification System

## Project Overview

The Email AI project is an intelligent email classification system designed to analyze email threads between complainants and agencies. The system uses OpenAI's GPT models to automatically categorize complaint resolution status based on email conversations. This is particularly useful for customer service departments, government agencies, and organizations that handle large volumes of complaint correspondence.

## Key Features

- **Intelligent Email Classification**: Automatically categorizes email threads into 5 distinct resolution statuses
- **Multi-threaded Email Processing**: Handles complex email conversations between multiple parties
- **RESTful API**: Easy integration with existing systems via HTTP endpoints
- **OpenAI Integration**: Leverages GPT-4o-mini for accurate natural language understanding
- **Secure Authentication**: API key-based authentication system
- **Error Handling**: Comprehensive error handling with detailed response codes

## Classification Categories

The system classifies email threads into the following categories:

1. **Resolved**: Complete resolution of all complaint aspects with complainant satisfaction
2. **Acknowledged**: Agency has confirmed receipt and is reviewing the complaint
3. **Partially Resolved**: Some issues addressed but additional actions still required
4. **Denied**: Agency has reviewed and determined the complaint is not valid or actionable
5. **More Information Asked**: Agency requires additional details to proceed with resolution

## Project Structure

```
R_email/
├── app.py                 # Main Flask application and API endpoints
├── Email_Agent.py         # Core email classification logic
├── milvus_config.py       # Database configuration (Milvus vector database)
├── milvus_utils.py        # Database utility functions
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
├── Prompt/               # Classification prompts and templates
│   ├── classifier.py     # Main classification prompt
│   ├── denied.py         # Denial response templates
│   ├── more_info.py      # Information request templates
│   └── partially_resolved.py # Partial resolution templates
├── rohan.ipynb           # Development notebook
└── test.ipynb            # Testing notebook
```

## Technical Architecture

### Core Components

1. **Flask Web Application** (`app.py`):
   - RESTful API endpoint `/ai_email`
   - Request validation and authentication
   - Error handling and response formatting
   - CORS support for cross-origin requests

2. **Email Agent** (`Email_Agent.py`):
   - Email thread processing and restructuring
   - OpenAI API integration for classification
   - JSON response parsing and validation

3. **Classification System** (`Prompt/classifier.py`):
   - Detailed prompt engineering for accurate classification
   - Category definitions with specific criteria
   - JSON-formatted response structure

### Dependencies

- **Flask 3.0.3**: Web framework for API development
- **OpenAI 1.23.2**: AI model integration for text classification
- **PyMilvus 2.4.0**: Vector database for potential future enhancements
- **Pandas 2.2.2**: Data manipulation and analysis
- **TikToken 0.7.0**: Token counting for OpenAI API optimization
- **Python-dotenv 1.0.1**: Environment variable management

## Installation & Setup

### Prerequisites

- Python 3.8 or higher
- OpenAI API key
- Environment variables configuration

### Installation Steps

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd R_email
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration**:
   Create a `.env` file in the project root:
   ```env
   openai_api_key=your_openai_api_key_here
   api_key=your_api_authentication_key
   MILVUS_HOST=localhost
   MILVUS_PORT=19530
   GPT_COMPLETION=gpt-4o-mini
   GPT_EMBEDDINGS=text-embedding-ada-002
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

The API will be available at `http://localhost:5005`

## API Usage

### Authentication

All requests require an API key in the Authorization header:
```
Authorization: your_api_key_here
```

### Endpoint: POST /ai_email

**Request Format**:
```json
[
  {
    "user": {
      "id": 1,
      "sender_type": "user",
      "subject": "Complaint Subject",
      "body": "Detailed complaint description..."
    }
  },
  {
    "agency": {
      "id": 2,
      "sender_type": "agency",
      "subject": "Response Subject",
      "body": "Agency response content..."
    }
  }
]
```

**Response Format**:
```json
{
    "data": {
        "mail_status": "resolved"
    },
    "code": 200,
    "message": "Request Successful"
}
```

### Status Codes

- **200**: Successful classification
- **401**: Unauthorized (invalid API key)
- **429**: Rate limit exceeded (OpenAI quota)
- **500**: Internal server error

## Development & Testing

### Jupyter Notebooks

- **rohan.ipynb**: Development and experimentation notebook
- **test.ipynb**: Testing and validation notebook

### Testing the API

Use tools like Postman, curl, or Python requests:

```python
import requests

url = "http://localhost:5005/ai_email"
headers = {
    "Content-Type": "application/json",
    "Authorization": "your_api_key"
}
data = [
    {
        "user": {
            "id": 1,
            "sender_type": "user",
            "subject": "Service Complaint",
            "body": "I am unsatisfied with the service provided..."
        }
    }
]

response = requests.post(url, json=data, headers=headers)
print(response.json())
```

## Configuration

### OpenAI Configuration

- **Model**: GPT-4o-mini (cost-effective and efficient)
- **Temperature**: 0.1 (low randomness for consistent classification)
- **Max Tokens**: 30 (sufficient for category response)
- **Response Format**: JSON object for structured output

### Token Management

- **Conversation Limit**: 14,000 tokens
- **Query Limit**: 8,000 tokens
- **Encoding**: cl100k_base (GPT-4 compatible)

## Future Enhancements

1. **Vector Database Integration**: Full Milvus implementation for similarity search
2. **Response Generation**: Automated reply generation based on classification
3. **Multi-language Support**: Classification in multiple languages
4. **Analytics Dashboard**: Web interface for classification statistics
5. **Batch Processing**: Handle multiple email threads simultaneously

## Troubleshooting

### Common Issues

1. **OpenAI Rate Limits**: Implement exponential backoff and retry logic
2. **Authentication Errors**: Verify API key configuration in environment variables
3. **JSON Parsing Errors**: Ensure proper email thread structure in requests
4. **Memory Issues**: Monitor token usage for large email threads

### Logging

The application includes comprehensive error logging with line numbers for debugging:
```python
error_message = f"Error on line {sys.exc_info()[-1].tb_lineno}: {str(e)}"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with proper testing
4. Submit a pull request with detailed description

## License

This project is proprietary software. All rights reserved.

## Support

For technical support or questions, please contact the development team or create an issue in the project repository.
