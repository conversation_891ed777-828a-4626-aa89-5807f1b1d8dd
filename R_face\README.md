# Facial Recognition System - Crime & Violation Tracking

## Project Overview

The Facial Recognition System is a comprehensive computer vision application designed for law enforcement and security agencies to track individuals across multiple incidents. The system processes video files and images to extract, identify, and catalog faces, maintaining a persistent database of individuals with their associated crime reports, violation records, and location data.

This system is particularly valuable for:
- **Law Enforcement**: Track repeat offenders across multiple incidents
- **Security Agencies**: Monitor individuals across different locations and time periods
- **Investigation Support**: Provide comprehensive profiles of suspects with historical data
- **Crime Pattern Analysis**: Identify frequent violators and crime hotspots

## Key Features

### Core Functionality
- **Multi-format Processing**: Supports both video (.mp4) and image files (.jpg, .jpeg, .png, .webp)
- **Real-time Face Detection**: Uses dlib's state-of-the-art face detection algorithms
- **Face Recognition**: Advanced facial encoding using ResNet-based deep learning models
- **Persistent Storage**: MySQL database integration for long-term data retention
- **Duplicate Detection**: Intelligent face matching to prevent duplicate entries
- **Incremental Learning**: Updates face encodings as more samples are processed

### Advanced Analytics
- **Frequency Analysis**: Identify individuals who appear in multiple incidents
- **Top Offenders**: Rank individuals by number of incidents
- **Location Tracking**: Geographic correlation of incidents
- **Report Classification**: Categorize incidents as crimes or violations
- **Historical Profiling**: Comprehensive individual profiles with incident history

### API Endpoints
- **Face Upload & Processing**: Batch process videos and images
- **Face Identification**: Query database with new images
- **Frequency Analysis**: Find repeat offenders
- **Top Faces Retrieval**: Get most frequently encountered individuals

## Technical Architecture

### Core Technologies
- **Computer Vision**: OpenCV 4.7.0 for image/video processing
- **Face Detection**: dlib 19.24.1 with HOG + Linear SVM detector
- **Face Recognition**: dlib's ResNet-based face recognition model
- **Machine Learning**: scikit-learn for PCA and similarity calculations
- **Web Framework**: Flask 2.3.2 for RESTful API
- **Database**: MySQL with SQLAlchemy ORM
- **Image Processing**: PIL/Pillow for image manipulation

### Machine Learning Pipeline
1. **Face Detection**: Histogram of Oriented Gradients (HOG) feature extraction
2. **Landmark Detection**: 68-point facial landmark identification
3. **Face Encoding**: 128-dimensional face descriptor generation using ResNet
4. **Similarity Matching**: Euclidean distance calculation for face comparison
5. **Incremental Learning**: Running average updates for face encodings

## Project Structure

```
R_face/
├── app.py                    # Main Flask application with all endpoints
├── config.py                 # Configuration settings (database, paths)
├── requirements.txt          # Python dependencies
├── README.md                # Project documentation
├── Procfile                 # Heroku deployment configuration
├── Aptfile                  # System dependencies for deployment
├── models/                  # Pre-trained dlib models
│   ├── dlib_face_recognition_resnet_model_v1.dat
│   ├── shape_predictor_68_face_landmarks.dat
│   └── shape_predictor_5_face_landmarks.dat
├── demo_files/              # Sample images and videos for testing
├── Faces Recorded/          # Processed face images storage
└── .env                     # Environment variables (not in repo)
```

## Installation & Setup

### Prerequisites
- Python 3.8 or higher
- MySQL Server 8.0+
- OpenCV compatible system libraries
- Sufficient storage for face image database

### System Dependencies (Linux/Ubuntu)
```bash
sudo apt-get update
sudo apt-get install -y \
    libgl1-mesa-dev \
    libgl1-mesa-glx \
    libonig-dev \
    libxcb.*-dev \
    libx11-xcb-dev \
    libglu1-mesa-dev \
    libxrender-dev \
    libxi-dev \
    libxkbcommon-dev \
    libxkbcommon-x11-dev \
    libxcb-util-dev
```

### Installation Steps

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd R_face
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **MySQL Database Setup**:
   ```sql
   CREATE DATABASE face_schema;
   CREATE USER 'face_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON face_schema.* TO 'face_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Environment Configuration**:
   Create a `.env` file in the project root:
   ```env
   OUTPUT_DIR=Faces Recorded
   DOMAIN=http://localhost:5000
   USERNAME_SQL=face_user
   PASSWORD=secure_password
   HOST_SQL=localhost
   DATABASE=face_schema
   TABLE=face_record
   ```

5. **Download Pre-trained Models**:
   The models folder should contain:
   - `dlib_face_recognition_resnet_model_v1.dat` (22.5 MB)
   - `shape_predictor_68_face_landmarks.dat` (99.7 MB)
   - `shape_predictor_5_face_landmarks.dat` (9.2 MB)

6. **Run the application**:
   ```bash
   python app.py
   ```

The API will be available at `http://localhost:5000`

## API Documentation

### 1. Upload and Process Files

**Endpoint**: `POST /upload`

**Description**: Process video or image files to extract and register faces

**Parameters**:
- `file`: Video (.mp4) or image file (.jpg, .jpeg, .png, .webp)
- `report id`: Unique identifier for the incident report
- `report type`: Classification ("crime" or "violation")
- `location`: Geographic coordinates or location description

**Example Request**:
```bash
curl -X POST \
  http://localhost:5000/upload \
  -F "file=@incident_video.mp4" \
  -F "report id=RPT-2024-001" \
  -F "report type=crime" \
  -F "location=**********.N, **********.E"
```

**Response**:
```
Faces in incident_video.mp4 uploaded to database
```

### 2. Face Identification

**Endpoint**: `POST /info`

**Description**: Identify a person from an uploaded image

**Parameters**:
- `file`: Image file containing a face to identify

**Example Request**:
```bash
curl -X POST \
  http://localhost:5000/info \
  -F "file=@suspect_photo.jpg"
```

**Response**:
```json
{
  "message": "The face Id 15 is found in 3 videos.",
  "Report IDs": ["104", "112", "113"],
  "Report Types": ["violation", "crime", "violation"],
  "Number of crimes reported": 1,
  "Number of violations reported": 2,
  "Was Seen at these locations": [
    "**********.N, **********.E",
    "*********.N, **********.E",
    "**********.N, **********.E"
  ],
  "Files": ["incident_video.mp4", "security_cam_02.mp4", "patrol_footage.mp4"]
}
```

### 3. Frequent Offenders Analysis

**Endpoint**: `GET /frequent?threshold=<number>`

**Description**: Find individuals who appear in multiple incidents

**Parameters**:
- `threshold`: Minimum number of incidents to qualify as frequent offender

**Example Request**:
```bash
curl "http://localhost:5000/frequent?threshold=3"
```

**Response**:
```
Faces 5,12,18,23 have occurred more than 3.
```

### 4. Top Faces Retrieval

**Endpoint**: `GET /top_faces?top_k_faces=<number>`

**Description**: Get URLs of the most frequently encountered faces

**Parameters**:
- `top_k_faces`: Number of top faces to retrieve

**Example Request**:
```bash
curl "http://localhost:5000/top_faces?top_k_faces=5"
```

**Response**:
```json
[
  "http://localhost:5000/top_faces/1.jpg",
  "http://localhost:5000/top_faces/5.jpg",
  "http://localhost:5000/top_faces/12.jpg",
  "http://localhost:5000/top_faces/18.jpg",
  "http://localhost:5000/top_faces/23.jpg"
]
```

### 5. Face Image Access

**Endpoint**: `GET /top_faces/<face_id>.jpg`

**Description**: Retrieve the stored image of a specific face

**Example Request**:
```bash
curl "http://localhost:5000/top_faces/15.jpg" --output face_15.jpg
```

## Database Schema

The system uses a MySQL table with the following structure:

```sql
CREATE TABLE face_record (
    name TEXT,                    -- Unique face identifier
    count BIGINT,                 -- Total number of detections
    videos TEXT,                  -- Comma-separated list of source files
    video_count BIGINT,           -- Number of unique incidents
    report_id TEXT,               -- Comma-separated report IDs
    report_type TEXT,             -- Comma-separated report types
    location TEXT,                -- Comma-separated locations
    closest_encoding TEXT,        -- Best quality face encoding
    encodings TEXT                -- Average face encoding
);
```

## Algorithm Details

### Face Detection Process
1. **Frame Extraction**: Videos processed at 24-frame intervals for efficiency
2. **Preprocessing**: Frame resizing and grayscale conversion
3. **Face Detection**: dlib's HOG-based frontal face detector
4. **Quality Assessment**: Face size and clarity evaluation

### Face Recognition Pipeline
1. **Landmark Detection**: 68-point facial landmark identification
2. **Face Alignment**: Geometric normalization based on landmarks
3. **Feature Extraction**: 128-dimensional face descriptor using ResNet
4. **Similarity Calculation**: Euclidean distance comparison (threshold: 0.47)
5. **Identity Assignment**: New face creation or existing face update

### Incremental Learning
- **Running Average**: Face encodings updated with each new detection
- **Quality Selection**: Best quality face image retained for display
- **Metadata Aggregation**: Incident data accumulated across encounters

## Performance Optimization

### Processing Efficiency
- **Frame Skipping**: Process every 24th frame in videos (1 FPS at 24 FPS input)
- **Resolution Scaling**: Reduce frame size by 2.5x for faster processing
- **Batch Processing**: Multiple faces processed per frame
- **Memory Management**: Efficient numpy array operations

### Accuracy Improvements
- **Multiple Samples**: Average encodings across multiple detections
- **Quality Filtering**: Retain highest quality face images
- **Threshold Tuning**: Optimized similarity thresholds for different media types
- **Landmark Validation**: Ensure proper face alignment before encoding

## Deployment

### Local Development
```bash
python app.py
# Runs on http://localhost:5000
```

### Production Deployment (Heroku)
The project includes Heroku configuration files:
- `Procfile`: Gunicorn WSGI server configuration
- `Aptfile`: System dependencies for Heroku stack
- `requirements.txt`: Python dependencies

```bash
heroku create your-face-recognition-app
git push heroku main
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-dev libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000
CMD ["gunicorn", "app:app", "--workers=4", "--bind=0.0.0.0:5000"]
```

## Security Considerations

### Data Protection
- **Database Security**: Use strong passwords and encrypted connections
- **File Access**: Restrict access to face image storage directory
- **API Security**: Implement authentication for production use
- **Privacy Compliance**: Ensure compliance with local privacy laws

### Recommended Security Measures
- Enable MySQL SSL connections
- Implement API rate limiting
- Add user authentication and authorization
- Regular security audits and updates
- Data retention policies

## Troubleshooting

### Common Issues

1. **dlib Installation Errors**:
   ```bash
   # Install cmake first
   pip install cmake
   pip install dlib
   ```

2. **OpenCV Import Errors**:
   ```bash
   # Install system dependencies
   sudo apt-get install libgl1-mesa-glx
   ```

3. **MySQL Connection Issues**:
   - Verify MySQL service is running
   - Check credentials in `.env` file
   - Ensure database exists and user has permissions

4. **Memory Issues with Large Videos**:
   - Increase frame skip interval
   - Reduce frame scale factor
   - Process videos in smaller segments

### Performance Monitoring
- Monitor MySQL query performance
- Track face detection accuracy rates
- Monitor storage usage growth
- Log processing times for optimization

## Future Enhancements

### Planned Features
1. **Real-time Video Streaming**: Live camera feed processing
2. **Advanced Analytics**: Crime pattern analysis and prediction
3. **Multi-camera Correlation**: Track individuals across camera networks
4. **Mobile App Integration**: Field officers can query the system
5. **Export Capabilities**: Generate reports and evidence packages
6. **Advanced Search**: Search by location, time range, or incident type

### Technical Improvements
1. **GPU Acceleration**: CUDA support for faster processing
2. **Distributed Processing**: Multi-server deployment capability
3. **Advanced ML Models**: Integration with newer face recognition models
4. **Automated Quality Assessment**: Intelligent face quality scoring
5. **Backup and Recovery**: Automated database backup systems

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## License

This project is proprietary software designed for law enforcement and security applications. All rights reserved.

## Support

For technical support, bug reports, or feature requests, please contact the development team or create an issue in the project repository.

## Disclaimer

This facial recognition system is designed for legitimate law enforcement and security purposes. Users must ensure compliance with local privacy laws, data protection regulations, and ethical guidelines when deploying this system. The developers are not responsible for misuse of this technology.
